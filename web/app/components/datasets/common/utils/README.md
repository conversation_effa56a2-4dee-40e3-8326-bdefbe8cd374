# 全局检索配置管理

这个模块提供了一套完整的全局检索配置管理功能，允许在不同组件间共享和持久化检索方法配置。

## 功能特性

- ✅ **持久化存储**: 使用 localStorage 保存配置，页面刷新后仍然保持
- ✅ **配置验证**: 自动验证配置的有效性
- ✅ **事件通知**: 配置变化时自动通知其他组件
- ✅ **版本控制**: 支持配置版本管理和元数据
- ✅ **类型安全**: 完整的 TypeScript 类型支持
- ✅ **错误处理**: 完善的错误处理和日志记录

## 核心 API

### 保存配置

```typescript
import { saveGlobalRetrievalConfig } from './global-retrieval-config'

const config: RetrievalConfig = {
  search_method: RETRIEVE_METHOD.hybrid,
  reranking_enable: true,
  top_k: 5,
  // ... 其他配置
}

// 保存用户设置
saveGlobalRetrievalConfig(config, 'user_setting')

// 保存默认配置
saveGlobalRetrievalConfig(config, 'default')

// 保存导入的配置
saveGlobalRetrievalConfig(config, 'imported')
```

### 加载配置

```typescript
import { loadGlobalRetrievalConfig } from './global-retrieval-config'

const globalConfig = loadGlobalRetrievalConfig()
if (globalConfig) {
  console.log('检索方法:', globalConfig.search_method)
  console.log('保存时间:', new Date(globalConfig.timestamp))
  console.log('配置来源:', globalConfig.source)
}
```

### 配置验证

```typescript
import { validateRetrievalConfig } from './global-retrieval-config'

if (validateRetrievalConfig(config)) {
  // 配置有效，可以安全使用
  saveGlobalRetrievalConfig(config)
} else {
  console.error('配置无效')
}
```

### 清除配置

```typescript
import { clearGlobalRetrievalConfig } from './global-retrieval-config'

clearGlobalRetrievalConfig()
```

### 监听配置变化

```typescript
import { useGlobalRetrievalConfigListener } from './global-retrieval-config'

// 在 React 组件中使用
useEffect(() => {
  const cleanup = useGlobalRetrievalConfigListener(
    (newConfig) => {
      console.log('配置已更新:', newConfig)
      // 更新组件状态
    },
    () => {
      console.log('配置已清除')
      // 重置组件状态
    }
  )
  
  return cleanup
}, [])
```

## 在组件中的使用示例

### 1. 在检索方法配置组件中

```typescript
// retrieval-method-config-xiyan/index.tsx
const changeRetrievalMethod = (retrievalMethod: RETRIEVE_METHOD) => {
  const config = {
    ...value,
    search_method: retrievalMethod,
  } as RetrievalConfig
  
  onChange(config)
  
  // 保存到全局状态
  if (validateRetrievalConfig(config)) {
    saveGlobalRetrievalConfig(config, 'user_setting')
  }
}

// 组件初始化时加载全局配置
useEffect(() => {
  if (!passValue && supportRetrievalMethods.length > 0) {
    const globalConfig = loadGlobalRetrievalConfig()
    
    if (globalConfig && supportRetrievalMethods.includes(globalConfig.search_method)) {
      // 使用全局保存的配置
      setCurrentRetrievalMethod(globalConfig.search_method)
      onChange(globalConfig)
    } else {
      // 使用默认配置
      const defaultConfig = createDefaultConfig()
      onChange(defaultConfig)
    }
  }
}, [supportRetrievalMethods, passValue, onChange])
```

### 2. 在知识增强设置组件中

```typescript
// knowledge-enhancement-setting/index.tsx
useEffect(() => {
  // 监听全局配置变化，同步 Embedding 模型选择
  const cleanup = useGlobalRetrievalConfigListener(
    (newConfig) => {
      if (newConfig.weights?.vector_setting) {
        const { embedding_provider_name, embedding_model_name } = newConfig.weights.vector_setting
        if (embedding_provider_name && embedding_model_name) {
          // 更新 Embedding 模型选择
          updateEmbeddingModel(embedding_provider_name, embedding_model_name)
        }
      }
    }
  )
  
  return cleanup
}, [])
```

## 配置结构

```typescript
interface GlobalRetrievalConfig extends RetrievalConfig {
  timestamp: number        // 保存时间戳
  version: string         // 配置版本
  source: 'user_setting' | 'default' | 'imported'  // 配置来源
}
```

## 最佳实践

1. **配置验证**: 保存前始终验证配置的有效性
2. **错误处理**: 妥善处理加载和保存过程中的错误
3. **事件监听**: 使用事件监听器保持组件间的同步
4. **清理资源**: 组件卸载时清理事件监听器
5. **用户体验**: 提供配置状态的可视化反馈

## 注意事项

- localStorage 有存储大小限制（通常 5-10MB）
- 配置变化会触发全局事件，避免在事件处理中再次保存配置造成循环
- 在 SSR 环境中，localStorage 可能不可用，需要适当的错误处理
- 定期清理过期或无效的配置数据

## 测试

运行测试：

```bash
npm test global-retrieval-config.test.ts
```

测试覆盖了所有核心功能，包括保存、加载、验证、清除和事件通知。
