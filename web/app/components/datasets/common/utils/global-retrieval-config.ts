import type { RetrievalConfig } from '@/types/datasets'

// 全局检索配置的存储键
const GLOBAL_RETRIEVAL_CONFIG_KEY = 'global_retrieval_config'

// 扩展的全局检索配置类型，包含元数据
export interface GlobalRetrievalConfig extends RetrievalConfig {
  timestamp: number
  version: string
  source: 'user_setting' | 'default' | 'imported'
}

/**
 * 保存检索配置到全局状态
 * @param config 检索配置
 * @param source 配置来源
 */
export const saveGlobalRetrievalConfig = (
  config: RetrievalConfig,
  source: GlobalRetrievalConfig['source'] = 'user_setting'
): boolean => {
  try {
    const globalConfig: GlobalRetrievalConfig = {
      ...config,
      timestamp: Date.now(),
      version: '1.0.0',
      source,
    }
    
    localStorage.setItem(GLOBAL_RETRIEVAL_CONFIG_KEY, JSON.stringify(globalConfig))
    console.log('检索方法配置已保存到全局状态:', globalConfig)
    
    // 触发自定义事件，通知其他组件配置已更新
    window.dispatchEvent(new CustomEvent('globalRetrievalConfigChanged', {
      detail: globalConfig
    }))
    
    return true
  } catch (error) {
    console.error('保存检索方法配置到全局状态失败:', error)
    return false
  }
}

/**
 * 从全局状态加载检索配置
 * @returns 检索配置或null
 */
export const loadGlobalRetrievalConfig = (): GlobalRetrievalConfig | null => {
  try {
    const savedConfig = localStorage.getItem(GLOBAL_RETRIEVAL_CONFIG_KEY)
    if (savedConfig) {
      const parsedConfig = JSON.parse(savedConfig) as GlobalRetrievalConfig
      console.log('从全局状态加载检索配置:', parsedConfig)
      return parsedConfig
    }
  } catch (error) {
    console.error('从全局状态加载检索配置失败:', error)
  }
  return null
}

/**
 * 清除全局检索配置
 */
export const clearGlobalRetrievalConfig = (): boolean => {
  try {
    localStorage.removeItem(GLOBAL_RETRIEVAL_CONFIG_KEY)
    console.log('全局检索配置已清除')
    
    // 触发自定义事件，通知其他组件配置已清除
    window.dispatchEvent(new CustomEvent('globalRetrievalConfigCleared'))
    
    return true
  } catch (error) {
    console.error('清除全局检索配置失败:', error)
    return false
  }
}

/**
 * 检查全局配置是否存在
 */
export const hasGlobalRetrievalConfig = (): boolean => {
  return localStorage.getItem(GLOBAL_RETRIEVAL_CONFIG_KEY) !== null
}

/**
 * 获取全局配置的元数据
 */
export const getGlobalRetrievalConfigMetadata = (): Pick<GlobalRetrievalConfig, 'timestamp' | 'version' | 'source'> | null => {
  const config = loadGlobalRetrievalConfig()
  if (config) {
    return {
      timestamp: config.timestamp,
      version: config.version,
      source: config.source,
    }
  }
  return null
}

/**
 * 监听全局配置变化的Hook
 */
export const useGlobalRetrievalConfigListener = (
  onConfigChanged?: (config: GlobalRetrievalConfig) => void,
  onConfigCleared?: () => void
) => {
  const handleConfigChanged = (event: CustomEvent<GlobalRetrievalConfig>) => {
    onConfigChanged?.(event.detail)
  }
  
  const handleConfigCleared = () => {
    onConfigCleared?.()
  }
  
  // 添加事件监听器
  window.addEventListener('globalRetrievalConfigChanged', handleConfigChanged as EventListener)
  window.addEventListener('globalRetrievalConfigCleared', handleConfigCleared)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('globalRetrievalConfigChanged', handleConfigChanged as EventListener)
    window.removeEventListener('globalRetrievalConfigCleared', handleConfigCleared)
  }
}

/**
 * 验证检索配置的有效性
 */
export const validateRetrievalConfig = (config: any): config is RetrievalConfig => {
  return (
    config &&
    typeof config === 'object' &&
    typeof config.search_method === 'string' &&
    typeof config.reranking_enable === 'boolean' &&
    typeof config.top_k === 'number' &&
    typeof config.score_threshold_enabled === 'boolean' &&
    typeof config.score_threshold === 'number' &&
    config.reranking_model &&
    typeof config.reranking_model.reranking_provider_name === 'string' &&
    typeof config.reranking_model.reranking_model_name === 'string'
  )
}

/**
 * 合并配置，优先使用新配置的非空值
 */
export const mergeRetrievalConfigs = (
  baseConfig: RetrievalConfig,
  newConfig: Partial<RetrievalConfig>
): RetrievalConfig => {
  return {
    ...baseConfig,
    ...newConfig,
    reranking_model: {
      ...baseConfig.reranking_model,
      ...newConfig.reranking_model,
    },
    weights: newConfig.weights ? {
      ...baseConfig.weights,
      ...newConfig.weights,
      vector_setting: {
        ...baseConfig.weights?.vector_setting,
        ...newConfig.weights.vector_setting,
      },
      keyword_setting: {
        ...baseConfig.weights?.keyword_setting,
        ...newConfig.weights.keyword_setting,
      },
    } : baseConfig.weights,
  }
}
