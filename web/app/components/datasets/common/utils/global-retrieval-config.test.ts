import {
  saveGlobalRetrievalConfig,
  loadGlobalRetrievalConfig,
  clearGlobalRetrievalConfig,
  hasGlobalRetrievalConfig,
  validateRetrievalConfig,
  mergeRetrievalConfigs,
} from './global-retrieval-config'
import { RETRIEVE_METHOD, RerankingModeEnum, WeightedScoreEnum } from '@/types/datasets'
import type { RetrievalConfig } from '@/types/datasets'

// Mock localStorage for testing
const localStorageMock = (() => {
  let store: Record<string, string> = {}

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString()
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    },
  }
})()

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock window.dispatchEvent
Object.defineProperty(window, 'dispatchEvent', {
  value: jest.fn(),
})

describe('全局检索配置管理', () => {
  const mockConfig: RetrievalConfig = {
    search_method: RETRIEVE_METHOD.hybrid,
    reranking_enable: true,
    reranking_mode: RerankingModeEnum.WeightedScore,
    reranking_model: {
      reranking_provider_name: 'tong_yi',
      reranking_model_name: 'gte-rerank',
    },
    weights: {
      weight_type: WeightedScoreEnum.Customized,
      vector_setting: {
        vector_weight: 0.7,
        embedding_provider_name: 'test_provider',
        embedding_model_name: 'test_model',
      },
      keyword_setting: {
        keyword_weight: 0.3,
      },
    },
    top_k: 5,
    score_threshold_enabled: true,
    score_threshold: 0.6,
  }

  beforeEach(() => {
    localStorageMock.clear()
    jest.clearAllMocks()
  })

  describe('saveGlobalRetrievalConfig', () => {
    it('应该成功保存配置到localStorage', () => {
      const result = saveGlobalRetrievalConfig(mockConfig, 'user_setting')
      
      expect(result).toBe(true)
      expect(localStorageMock.getItem('global_retrieval_config')).toBeTruthy()
      
      const saved = JSON.parse(localStorageMock.getItem('global_retrieval_config')!)
      expect(saved.search_method).toBe(RETRIEVE_METHOD.hybrid)
      expect(saved.source).toBe('user_setting')
      expect(saved.timestamp).toBeDefined()
      expect(saved.version).toBe('1.0.0')
    })

    it('应该触发自定义事件', () => {
      saveGlobalRetrievalConfig(mockConfig)
      
      expect(window.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'globalRetrievalConfigChanged',
        })
      )
    })
  })

  describe('loadGlobalRetrievalConfig', () => {
    it('应该成功加载保存的配置', () => {
      saveGlobalRetrievalConfig(mockConfig, 'user_setting')
      
      const loaded = loadGlobalRetrievalConfig()
      
      expect(loaded).toBeTruthy()
      expect(loaded!.search_method).toBe(RETRIEVE_METHOD.hybrid)
      expect(loaded!.source).toBe('user_setting')
    })

    it('当没有保存的配置时应该返回null', () => {
      const loaded = loadGlobalRetrievalConfig()
      
      expect(loaded).toBeNull()
    })

    it('当localStorage数据损坏时应该返回null', () => {
      localStorageMock.setItem('global_retrieval_config', 'invalid json')
      
      const loaded = loadGlobalRetrievalConfig()
      
      expect(loaded).toBeNull()
    })
  })

  describe('clearGlobalRetrievalConfig', () => {
    it('应该成功清除配置', () => {
      saveGlobalRetrievalConfig(mockConfig)
      expect(hasGlobalRetrievalConfig()).toBe(true)
      
      const result = clearGlobalRetrievalConfig()
      
      expect(result).toBe(true)
      expect(hasGlobalRetrievalConfig()).toBe(false)
    })

    it('应该触发清除事件', () => {
      clearGlobalRetrievalConfig()
      
      expect(window.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'globalRetrievalConfigCleared',
        })
      )
    })
  })

  describe('validateRetrievalConfig', () => {
    it('应该验证有效的配置', () => {
      expect(validateRetrievalConfig(mockConfig)).toBe(true)
    })

    it('应该拒绝无效的配置', () => {
      expect(validateRetrievalConfig(null)).toBe(false)
      expect(validateRetrievalConfig({})).toBe(false)
      expect(validateRetrievalConfig({ search_method: 'invalid' })).toBe(false)
    })
  })

  describe('mergeRetrievalConfigs', () => {
    it('应该正确合并配置', () => {
      const baseConfig = { ...mockConfig }
      const newConfig = {
        top_k: 10,
        reranking_model: {
          reranking_provider_name: 'new_provider',
        },
      }

      const merged = mergeRetrievalConfigs(baseConfig, newConfig)

      expect(merged.top_k).toBe(10)
      expect(merged.reranking_model.reranking_provider_name).toBe('new_provider')
      expect(merged.reranking_model.reranking_model_name).toBe('gte-rerank') // 保持原值
      expect(merged.search_method).toBe(RETRIEVE_METHOD.hybrid) // 保持原值
    })
  })
})
