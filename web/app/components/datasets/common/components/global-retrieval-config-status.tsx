'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  loadGlobalRetrievalConfig,
  clearGlobalRetrievalConfig,
  hasGlobalRetrievalConfig,
  getGlobalRetrievalConfigMetadata,
  type GlobalRetrievalConfig,
} from '../utils/global-retrieval-config'
import { RETRIEVE_METHOD } from '@/types/datasets'

/**
 * 全局检索配置状态显示组件
 * 用于显示当前全局保存的检索配置信息，并提供清除功能
 */
const GlobalRetrievalConfigStatus: React.FC = () => {
  const { t } = useTranslation()
  const [globalConfig, setGlobalConfig] = useState<GlobalRetrievalConfig | null>(null)
  const [hasConfig, setHasConfig] = useState(false)

  // 加载全局配置
  const loadConfig = () => {
    const config = loadGlobalRetrievalConfig()
    setGlobalConfig(config)
    setHasConfig(hasGlobalRetrievalConfig())
  }

  // 清除全局配置
  const handleClearConfig = () => {
    if (clearGlobalRetrievalConfig()) {
      setGlobalConfig(null)
      setHasConfig(false)
    }
  }

  // 监听全局配置变化
  useEffect(() => {
    loadConfig()

    const handleConfigChanged = (event: CustomEvent<GlobalRetrievalConfig>) => {
      setGlobalConfig(event.detail)
      setHasConfig(true)
    }

    const handleConfigCleared = () => {
      setGlobalConfig(null)
      setHasConfig(false)
    }

    window.addEventListener('globalRetrievalConfigChanged', handleConfigChanged as EventListener)
    window.addEventListener('globalRetrievalConfigCleared', handleConfigCleared)

    return () => {
      window.removeEventListener('globalRetrievalConfigChanged', handleConfigChanged as EventListener)
      window.removeEventListener('globalRetrievalConfigCleared', handleConfigCleared)
    }
  }, [])

  // 获取检索方法的显示名称
  const getSearchMethodName = (method: RETRIEVE_METHOD) => {
    switch (method) {
      case RETRIEVE_METHOD.hybrid:
        return '混合检索'
      case RETRIEVE_METHOD.semantic:
        return '语义检索'
      case RETRIEVE_METHOD.fullText:
        return '全文检索'
      case RETRIEVE_METHOD.invertedIndex:
        return '倒排索引'
      case RETRIEVE_METHOD.keywordSearch:
        return '关键词检索'
      default:
        return method
    }
  }

  // 格式化时间戳
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN')
  }

  if (!hasConfig) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">暂无全局检索配置</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
            <span className="text-sm font-medium text-blue-800">全局检索配置已保存</span>
          </div>
          
          {globalConfig && (
            <div className="space-y-1 text-xs text-blue-700">
              <div>
                <span className="font-medium">检索方法:</span> {getSearchMethodName(globalConfig.search_method)}
              </div>
              <div>
                <span className="font-medium">Top K:</span> {globalConfig.top_k}
              </div>
              <div>
                <span className="font-medium">重排序:</span> {globalConfig.reranking_enable ? '启用' : '禁用'}
              </div>
              {globalConfig.reranking_enable && (
                <div>
                  <span className="font-medium">重排序模型:</span> {globalConfig.reranking_model.reranking_provider_name}/{globalConfig.reranking_model.reranking_model_name}
                </div>
              )}
              <div>
                <span className="font-medium">分数阈值:</span> {globalConfig.score_threshold_enabled ? `启用 (${globalConfig.score_threshold})` : '禁用'}
              </div>
              <div>
                <span className="font-medium">保存时间:</span> {formatTimestamp(globalConfig.timestamp)}
              </div>
              <div>
                <span className="font-medium">配置来源:</span> {
                  globalConfig.source === 'user_setting' ? '用户设置' :
                  globalConfig.source === 'default' ? '默认配置' :
                  globalConfig.source === 'imported' ? '导入配置' : globalConfig.source
                }
              </div>
            </div>
          )}
        </div>
        
        <button
          onClick={handleClearConfig}
          className="ml-4 px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
        >
          清除配置
        </button>
      </div>
    </div>
  )
}

export default GlobalRetrievalConfigStatus
